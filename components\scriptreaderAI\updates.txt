Apply the updates to the code files as instructed below:
"use client"

import { useState, useEffect, useRef, useCallback, useMemo } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { MessageSquare, FileText, Mic, Loader, Info, Book, X, ChevronLeft, ChevronRight, MessageCircle, Video } from "lucide-react"
import { CompactThemeToggle } from "../ThemeToggle"
import ChatTab from "./ChatTab"
import { getFirestore, collection, query, where, getDocs, addDoc, serverTimestamp, orderBy, limit, doc, getDoc } from "firebase/firestore"
import { useGetNamespace } from "./useGetNamespace"
import { useSession } from "next-auth/react"
import { v4 as uuidv4 } from "uuid"
import useUpload, { StatusText } from "./useUpload"
import { useConversation } from "@elevenlabs/react"
import { db } from "components/firebase"
import SideBar from "./SideBar"
import Rehearsals from "./Rehearsals"
import FileDetails from "./FileDetails"
import ScriptTab from "./ScriptTab"
import ResponseTab from "./ResponseTab"
import { updateAgentVoice, getAgentConfiguration, extractAgentName, createElevenLabsClient, configureAgentClientTools } from "./elevenlabs"
import { useAgentModality, type AgentModalityType } from "./useAgentModality"
import { AVAILABLE_VOICES } from "./voiceUtils"

interface ScriptFile {
  id: string
  name: string
  namespace: string
}

interface ChatMessage {
  id?: string
  tempId?: string
  role: "user" | "assistant"
  content: string
  timestamp: string
  audioUrl?: string
  fileDocumentId?: string
}

interface ReadermodalProps {
  isOpen: boolean
  onClose: () => void
  fileId?: string
}

function Readermodal({ isOpen, onClose, fileId }: ReadermodalProps) {
  const [activeTab, setActiveTab] = useState<string | null>(null)
  const [activeSection, setActiveSection] = useState<string>("rehearsing")
  const [scriptFiles, setScriptFiles] = useState<ScriptFile[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState<boolean>(false)
  const [uploadProgress, setUploadProgress] = useState<number | null>(null)
  const [uploadStatusText, setUploadStatusText] = useState<string>("Uploading script...")
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [chatId, setChatId] = useState<string | null>(null)
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [fileDocumentId, setFileDocumentId] = useState<string | null>(null)
  const [selectedFileNamespace, setSelectedFileNamespace] = useState<string | null>(null)
  const [fileName, setFileName] = useState<string | null>(null)
  const isMounted = useRef(true)
  const messagesProcessingRef = useRef(false)
  const [hasPermission, setHasPermission] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [voiceErrorMessage, setVoiceErrorMessage] = useState("")
  const [apiConfigStatus, setApiConfigStatus] = useState<'unchecked' | 'valid' | 'invalid' | 'connecting'>('unchecked')
  const [detailedErrorInfo, setDetailedErrorInfo] = useState<string | null>(null)
  const { data: session, status: sessionStatus } = useSession()
  const userId = session?.user?.email || ""
  const { handleUpload, progress, status, uploadError } = useUpload()
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false)
  const [scriptContent, setScriptContent] = useState<string>("")
  const [isScriptLoading, setIsScriptLoading] = useState<boolean>(false)
  const [isScriptReady, setIsScriptReady] = useState<boolean>(false)
  const [isFormatting, setIsFormatting] = useState<boolean>(false)
  const [formattedMarkdown, setFormattedMarkdown] = useState<string>("")
  const [selectedVoiceId, setSelectedVoiceId] = useState<string | null>(null)
  const [isUpdatingVoice, setIsUpdatingVoice] = useState(false)
  const { agentModality, setAgentModality, updateAgentPrompt } = useAgentModality()
  const [agentName, setAgentName] = useState<string | null>(null)
  const [conversationMessages, setConversationMessages] = useState<Array<{ id: string; type: "user" | "assistant"; content: string; timestamp: Date; }>>([])
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null)
  const [sessionDuration, setSessionDuration] = useState(0)
  const [isRecording, setIsRecording] = useState(false)
  const [recordingError, setRecordingError] = useState<string | null>(null)
  const [recordingMode, setRecordingMode] = useState<'audio' | 'video'>('audio')
  const [recordingDuration, setRecordingDuration] = useState(0)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const [currentStream, setCurrentStream] = useState<MediaStream | null>(null)
  const [hasCameraPermission, setHasCameraPermission] = useState(false)
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null)
  const [recordings, setRecordings] = useState<Array<{ id: string; filename: string; url: string; timestamp: Date; rehearsalId: string; type: 'audio' | 'video'; duration?: number; fileSize?: number; }>>([])
  const [playingRecording, setPlayingRecording] = useState<string | null>(null)
  const [audioElements, setAudioElements] = useState<Record<string, HTMLAudioElement>>({})

  // NEW: State to manage the background agent configuration process
  const [agentConfigState, setAgentConfigState] = useState<'idle' | 'updating' | 'ready' | 'error'>('idle');
  const [agentConfigMessage, setAgentConfigMessage] = useState<string>('Select a voice and script to begin.');
  
  const clientTools = useMemo(() => {
    // ... (clientTools implementation remains the same)
  }, []);

  const conversation = useConversation({
    apiKey: process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY,
    agentId: process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID,
    clientTools: clientTools,
    onConnect: () => {
      console.log("Connected to ElevenLabs API successfully")
      setApiConfigStatus('valid')
      setDetailedErrorInfo(null)
      setSessionStartTime(new Date())
    },
    onDisconnect: () => {
        console.log("Disconnected from ElevenLabs");
        setIsListening(false);
    },
    onError: (error) => {
        console.error("ElevenLabs API error:", error)
        setVoiceErrorMessage("Connection error occurred.");
        setApiConfigStatus('invalid');
        setIsListening(false);
    },
    onMessage: (message) => {
      if (typeof message === "string") {
        setConversationMessages(prev => [...prev, { id: uuidv4(), type: "user", content: message, timestamp: new Date() }]);
      } else if (message && 'message' in message) {
        setConversationMessages(prev => [...prev, { id: uuidv4(), type: "assistant", content: message.message, timestamp: new Date() }]);
      }
    },
    // ... other conversation callbacks remain the same
  });

  const { status: voiceStatus, isSpeaking } = conversation;
  
  // NEW: Core logic to configure the agent in the background
  useEffect(() => {
    const configureAgentForRehearsal = async () => {
      // Guard clauses to prevent running unnecessarily
      if (isListening || !selectedVoiceId || !activeTab || !fileName || !agentName) {
        if (!selectedVoiceId) setAgentConfigMessage('Please select a voice to prepare the agent.');
        else if (!activeTab) setAgentConfigMessage('Please select a script to prepare the agent.');
        else setAgentConfigState('idle');
        return;
      }
      
      // Don't re-configure if already ready with the same settings
      if (agentConfigState === 'ready') return;

      console.log('[AGENT_CONFIG] 🚀 Starting background agent configuration...');
      setAgentConfigState('updating');
      setAgentConfigMessage(`Configuring ${agentName} for ${agentModality} mode...`);

      try {
        const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID;
        const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY;

        if (!agentId || !apiKey) throw new Error("Missing ElevenLabs Agent ID or API Key");
        
        console.log(`[AGENT_CONFIG] Updating agent prompt for modality: ${agentModality}`);
        await updateAgentPrompt(agentId, apiKey, {
          scriptName: fileName,
          agentName: agentName
        });
        
        console.log('[AGENT_CONFIG] ✅ Agent configuration successful!');
        setAgentConfigState('ready');
        setAgentConfigMessage(`${agentName} is ready for rehearsal.`);
        
      } catch (error) {
        console.error('[AGENT_CONFIG] ❌ Failed to configure agent:', error);
        setAgentConfigState('error');
        setAgentConfigMessage(`Error preparing agent. Please try again.`);
        setDetailedErrorInfo(error instanceof Error ? error.message : String(error));
      }
    };

    configureAgentForRehearsal();
  }, [agentModality, selectedVoiceId, activeTab, fileName, agentName, updateAgentPrompt, isListening, agentConfigState]);

  const fetchAgentName = useCallback(async () => {
    try {
      const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID;
      const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY;
      if (!agentId || !apiKey) return;

      const agentConfig = await getAgentConfiguration(agentId, apiKey);
      const extractedName = extractAgentName(agentConfig);
      setAgentName(extractedName);
      console.log(`[AGENT_CONFIG] Agent name fetched: "${extractedName}"`);
    } catch (error) {
      console.warn(`[AGENT_CONFIG] Failed to fetch agent name`, error);
      setAgentName('CastMate Assistant');
    }
  }, []);

  useEffect(() => {
    if (selectedVoiceId) {
      fetchAgentName();
    }
  }, [selectedVoiceId, fetchAgentName]);
  
  const handleVoiceSelect = async (voiceId: string) => {
    if (isUpdatingVoice || voiceId === selectedVoiceId) return;

    console.log(`[VOICE_SELECT] Starting voice selection for: ${voiceId}`);
    setIsUpdatingVoice(true);
    setAgentConfigState('updating');
    setAgentConfigMessage('Updating voice and personality...');
    
    // Set the voice ID immediately for UI responsiveness
    setSelectedVoiceId(voiceId);

    try {
      const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID;
      const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY;
      if (!agentId || !apiKey) throw new Error("Missing ElevenLabs configuration.");
      
      await updateAgentVoice(agentId, voiceId, apiKey);
      console.log(`[VOICE_SELECT] Voice updated to ${voiceId} on ElevenLabs.`);

      // The useEffect listening on selectedVoiceId will trigger fetchAgentName,
      // which then triggers the main configuration useEffect.
      // This creates a clean, cascading update flow.
    } catch (error) {
        console.error('[VOICE_SELECT] Error updating agent voice:', error);
        setAgentConfigState('error');
        setAgentConfigMessage('Failed to update voice.');
        setVoiceErrorMessage(error instanceof Error ? error.message : "Voice update failed.");
    } finally {
        setIsUpdatingVoice(false);
    }
  };

  const handleStartConversation = async () => {
    console.log("[CONVERSATION_START] Attempting to start streamlined conversation.");

    if (agentConfigState !== 'ready') {
      setVoiceErrorMessage("Agent is not ready. Please wait for configuration to complete.");
      console.warn(`[CONVERSATION_START] Blocked. Agent state is: ${agentConfigState}, message: ${agentConfigMessage}`);
      return;
    }
    if (!hasPermission) {
        setVoiceErrorMessage("Microphone access is required.");
        return;
    }
    if (!session?.user?.email) {
        setVoiceErrorMessage("You must be signed in.");
        return;
    }

    try {
      setApiConfigStatus('connecting');
      setIsListening(true);
      setVoiceErrorMessage("Connecting to rehearsal session...");

      await conversation.startSession();
      
      console.log("[CONVERSATION_START] ✅ Streamlined conversation started successfully.");
      setVoiceErrorMessage("");

    } catch (error) {
      console.error("[CONVERSATION_START] ❌ Error starting streamlined conversation:", error);
      setVoiceErrorMessage("Failed to start conversation. Please try again.");
      setDetailedErrorInfo(error instanceof Error ? error.message : String(error));
      setIsListening(false);
      setApiConfigStatus('invalid');
    }
  };
  
  const handleEndConversation = async () => {
    try {
      setIsListening(false)
      await conversation.endSession()
    } catch (error) {
      console.error("Error ending conversation:", error)
      setVoiceErrorMessage("Failed to end conversation")
    }
  };

  const handleSwitchToScriptTab = () => {
    setActiveSection('script');
  };
  
  const handleSetAgentModality = (modality: AgentModalityType) => {
    if (modality !== agentModality) {
      setAgentConfigState('idle'); // Force re-configuration
      setAgentModality(modality);
    }
  };

  // Keep all other functions like fetchScriptFiles, handleFileUpload, etc. unchanged from original
  // ... (toggleMute, fetchMostRecentFile, fetchScriptFiles, fetchFileDocumentId, fetchScriptContent, createNewChat, handleFileUpload, handleUploadClick, handleScriptDeleted, etc.)

  useEffect(() => {
    // Microphone permission request
    const requestMicPermission = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        setHasPermission(true);
        stream.getTracks().forEach(track => track.stop());
      } catch (error) {
        setHasPermission(false);
        setVoiceErrorMessage("Microphone access denied.");
      }
    };
    if (isOpen) {
        requestMicPermission();
    }
  }, [isOpen, activeSection]);

  useEffect(() => {
    if (sessionStatus === "authenticated") {
        fetchScriptFiles();
    }
  }, [sessionStatus, userId]);

  useEffect(() => {
    if (activeTab) {
        const selectedFile = scriptFiles.find(f => f.id === activeTab);
        if (selectedFile) {
            setFileName(selectedFile.name);
            setAgentConfigState('idle'); // Reset config state on script change
        }
    } else {
        setFileName(null);
    }
  }, [activeTab, scriptFiles]);
  
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 dark:bg-black/80 backdrop-blur-sm p-2 sm:p-4 md:p-0">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="relative w-full max-w-full sm:max-w-3xl md:max-w-4xl lg:max-w-6xl h-[90vh] sm:h-[85vh] bg-white dark:bg-gray-900 rounded-2xl shadow-2xl overflow-hidden border border-gray-200 dark:border-gray-700 transition-colors duration-300"
      >
        <div className="flex flex-col md:flex-row h-full">
            <AnimatePresence>
                <SideBar
                    activeTab={activeTab}
                    setActiveTab={setActiveTab}
                    scriptFiles={scriptFiles}
                    // ... other SideBar props from original
                />
            </AnimatePresence>

          <div className="flex-1 overflow-hidden backdrop-blur-sm">
            <div className="h-full flex flex-col">
              <div className="border-b border-gray-200 dark:border-gray-700 px-4 sm:px-6 py-3 sm:py-4 bg-gray-50 dark:bg-gray-800/50">
                {/* ... (Tab buttons for rehearsing, chat, script etc.) ... */}
              </div>

              <div className="flex-1 overflow-y-auto p-4 sm:p-6 scrollbar-thin">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={`${activeTab}-${activeSection}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2 }}
                    className="space-y-6 h-full"
                  >
                    {activeSection === "rehearsing" && (
                      <Rehearsals
                        agentConfigState={agentConfigState}
                        agentConfigMessage={agentConfigMessage}
                        apiConfigStatus={apiConfigStatus}
                        detailedErrorInfo={detailedErrorInfo}
                        isListening={isListening}
                        voiceStatus={voiceStatus}
                        isMuted={isMuted}
                        isSpeaking={isSpeaking}
                        hasPermission={hasPermission}
                        voiceErrorMessage={voiceErrorMessage}
                        toggleMute={toggleMute}
                        handleEndConversation={handleEndConversation}
                        handleStartConversation={handleStartConversation}
                        setVoiceErrorMessage={setVoiceErrorMessage}
                        selectedScriptName={fileName}
                        selectedVoiceId={selectedVoiceId}
                        onVoiceSelect={handleVoiceSelect}
                        isUpdatingVoice={isUpdatingVoice}
                        agentModality={agentModality}
                        onAgentModalityChange={handleSetAgentModality}
                        onSwitchToScriptTab={handleSwitchToScriptTab}
                        conversationMessages={conversationMessages}
                      />
                    )}

                    {/* ... Other sections: chat, script, response, details ... */}
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}

export { Readermodal };

--------------------------------------.
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Mic, Loader, MicOff, Volume2, VolumeX, Settings, WifiOff, CheckCircle, X, ChevronRight, MessageCircle, Briefcase, Book, AlertTriangle } from 'lucide-react';
import { useSession } from 'next-auth/react';
import VoiceCarousel from './VoiceCarousel';
import { getVoiceById } from './voiceUtils';
import { type AgentModalityType } from './useAgentModality';

interface RehearsalsProps {
  // NEW: Props to reflect the background agent configuration status
  agentConfigState: 'idle' | 'updating' | 'ready' | 'error';
  agentConfigMessage: string;

  // Existing props
  apiConfigStatus: 'unchecked' | 'valid' | 'invalid' | 'connecting';
  detailedErrorInfo: string | null;
  isListening: boolean;
  voiceStatus: string;
  isMuted: boolean;
  isSpeaking: boolean;
  hasPermission: boolean;
  voiceErrorMessage: string;
  toggleMute: () => Promise<void>;
  handleEndConversation: () => Promise<void>;
  handleStartConversation: () => Promise<void>;
  setVoiceErrorMessage: (message: string) => void;
  selectedScriptName?: string;
  selectedVoiceId: string | null;
  onVoiceSelect: (voiceId: string) => void;
  isUpdatingVoice?: boolean;
  agentModality: AgentModalityType;
  onAgentModalityChange: (modality: AgentModalityType) => void;
  onSwitchToScriptTab: () => void;
  conversationMessages: Array<{ id: string; type: 'user' | 'assistant'; content: string; timestamp: Date; }>;
}

const Rehearsals: React.FC<RehearsalsProps> = ({
  agentConfigState,
  agentConfigMessage,
  apiConfigStatus,
  detailedErrorInfo,
  isListening,
  voiceStatus,
  isMuted,
  isSpeaking,
  hasPermission,
  voiceErrorMessage,
  toggleMute,
  handleEndConversation,
  handleStartConversation,
  setVoiceErrorMessage,
  selectedScriptName,
  selectedVoiceId,
  onVoiceSelect,
  isUpdatingVoice = false,
  agentModality,
  onAgentModalityChange,
  onSwitchToScriptTab,
  conversationMessages
}) => {
  const { data: session } = useSession();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const handleVoiceSelection = (voiceId: string) => {
    onVoiceSelect(voiceId);
    setIsSidebarOpen(true);
  };

  const renderRehearseButton = () => {
    if (voiceStatus === "connected" || isListening) {
      return (
        <button
          onClick={handleEndConversation}
          className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg shadow-lg shadow-red-600/20 transition-colors"
        >
          <MicOff className="h-8 w-8" />
          <span className="text-sm">Stop Rehearsing</span>
        </button>
      );
    }

    const isDisabled = agentConfigState !== 'ready' || !hasPermission;
    let buttonContent;
    let buttonClass = `w-full flex items-center justify-center gap-2 px-6 py-3 text-white rounded-lg shadow-lg transition-colors `;

    if (isDisabled && agentConfigState !== 'updating' && agentConfigState !== 'error') {
        buttonClass += 'bg-gray-400 dark:bg-gray-700 opacity-50 cursor-not-allowed';
    }

    switch (agentConfigState) {
      case 'updating':
        buttonClass += 'bg-yellow-500 cursor-wait';
        buttonContent = (
          <>
            <Loader className="h-8 w-8 animate-spin" />
            <span className="text-sm">Preparing...</span>
          </>
        );
        break;
      case 'error':
        buttonClass += 'bg-red-600 hover:bg-red-700 cursor-pointer'; // Allow click to retry
        buttonContent = (
          <>
            <AlertTriangle className="h-8 w-8" />
            <span className="text-sm">Config Error</span>
          </>
        );
        break;
      case 'ready':
      case 'idle':
      default:
        buttonClass += isDisabled ? '' : 'bg-blue-600 hover:bg-blue-700 shadow-blue-600/20';
        buttonContent = (
          <>
            <Mic className="h-8 w-8" />
            <span className="text-sm">Rehearse</span>
          </>
        );
        break;
    }

    return (
      <button onClick={handleStartConversation} className={buttonClass} disabled={isDisabled}>
        {buttonContent}
      </button>
    );
  };
  
  const getStatusColor = () => {
    if (voiceStatus === 'connected') return 'green';
    if (agentConfigState === 'updating' || voiceStatus === 'connecting') return 'yellow';
    if (agentConfigState === 'error' || apiConfigStatus === 'invalid') return 'red';
    return 'gray';
  }

  return (
    <div className="relative h-full courier-font">
      <div className="absolute top-4 right-4 z-10">
        <motion.button
          onClick={() => setIsSidebarOpen(true)}
          className={`w-8 h-8 sm:w-24 flex items-center justify-center rounded-lg border transition-colors ${
            getStatusColor() === 'green' ? "bg-green-600/20 border-green-500/30 hover:bg-green-600/30" : 
            getStatusColor() === 'yellow' ? "bg-yellow-600/20 border-yellow-500/30 hover:bg-yellow-600/30 animate-pulse" : 
            "bg-gray-600/20 border-gray-500/30 hover:bg-gray-600/30"
          }`}
        >
          <ChevronLeft className="w-4 h-4 text-gray-400" />
          <span className="text-sm hidden sm:inline text-gray-400">Settings</span>
        </motion.button>
      </div>

      <div className="flex flex-col h-full overflow-y-auto custom-scrollbar">
        <div className="flex flex-col items-center justify-center py-8 space-y-6">
          <div className="text-center space-y-4 -mt-10">
            <h2 className="text-xl font-bold text-white">
              Script ready for rehearsal
            </h2>
            <div className="bg-blue-50 dark:bg-black/60 border border-blue-200 dark:border-white/10 rounded-lg p-4 min-w-[300px]">
              <p className="text-blue-700 dark:text-blue-400 text-lg font-medium">
                {selectedScriptName || "No script selected"}
              </p>
            </div>
            <div className={`backdrop-blur-sm rounded-lg p-3 min-w-[300px] mt-3 transition-all duration-300 border ${
                getStatusColor() === 'green' ? "bg-green-50 dark:bg-green-500/10 border-green-200 dark:border-green-500/20" :
                getStatusColor() === 'yellow' ? "bg-yellow-50 dark:bg-yellow-500/10 border-yellow-200 dark:border-yellow-500/20" :
                getStatusColor() === 'red' ? "bg-red-50 dark:bg-red-500/10 border-red-200 dark:border-red-500/20" :
                "bg-gray-50 dark:bg-gray-500/10 border-gray-200 dark:border-gray-500/20"
            }`}>
              <div className="flex items-center justify-center gap-3">
                {isSpeaking ? <motion.div className="w-5 h-5 bg-green-400 rounded-full" animate={{ scale: [1, 1.2, 1] }} transition={{ duration: 0.5, repeat: Infinity }} /> :
                 getStatusColor() === 'green' ? <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" /> :
                 getStatusColor() === 'yellow' ? <Loader className="w-5 h-5 text-yellow-600 dark:text-yellow-400 animate-spin" /> :
                 getStatusColor() === 'red' ? <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400" /> :
                 <WifiOff className="w-5 h-5 text-gray-500 dark:text-gray-400" />}
                <span className={`text-sm font-medium transition-colors duration-300 ${
                    getStatusColor() === 'green' ? "text-green-700 dark:text-green-300" :
                    getStatusColor() === 'yellow' ? "text-yellow-700 dark:text-yellow-300" :
                    getStatusColor() === 'red' ? "text-red-700 dark:text-red-300" :
                    "text-gray-600 dark:text-gray-400"
                }`}>
                    {agentConfigMessage}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex-1 px-6 pb-8">
          <VoiceCarousel
            selectedVoiceId={selectedVoiceId}
            onVoiceSelect={handleVoiceSelection}
            isUpdatingVoice={isUpdatingVoice}
          />
        </div>
      </div>
      
      <AnimatePresence>
        {isSidebarOpen && (
          <>
            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }} className="fixed inset-0 bg-black/50 z-40" onClick={() => setIsSidebarOpen(false)} />
            <motion.div initial={{ x: '100%' }} animate={{ x: 0 }} exit={{ x: '100%' }} transition={{ type: 'spring', damping: 25, stiffness: 200 }} className="fixed right-0 top-0 h-full w-80 bg-white/95 dark:bg-black/95 backdrop-blur-sm border-l border-gray-200 dark:border-white/10 z-50 overflow-y-auto scrollbar-thin">
              <div className="p-6">
                <div className="flex items-center mb-6">
                  <Settings className="w-5 h-5 text-gray-600 dark:text-gray-400 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Settings</h3>
                </div>
                
                {/* Agent Modality Settings, etc. */}
                <div className="mb-6 mt-3">
                    <h4 className="text-sm font-semibold text-gray-600 font dark:text-gray-400 mb-3">2 - Select Castmate Mode</h4>
                    {/* ... Modality selection buttons ... */}
                </div>

                <hr />
                <div className="mb-6 mt-3">
                  <h4 className="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-3">3 - Speak to Castmate</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <button onClick={toggleMute} disabled={voiceStatus !== "connected"} className={`w-full p-3 rounded-lg flex items-center justify-center space-x-2 transition-colors ${voiceStatus === "connected" ? 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600' : 'bg-gray-200 dark:bg-gray-700 opacity-50 cursor-not-allowed'}`}>
                      {isMuted ? <VolumeX className="h-8 w-8 text-gray-700 dark:text-white" /> : <Volume2 className="h-8 w-8 text-gray-700 dark:text-white" />}
                      <span className="text-sm text-gray-700 dark:text-white">{isMuted ? "Unmute" : "Mute"}</span>
                    </button>
                    {renderRehearseButton()}
                  </div>
                </div>

                <hr />
                <h4 className="text-sm font-semibold text-gray-600 font dark:text-gray-400 mb-3 mt-3">4 - Script Tab</h4>
                {/* ... Script tab button ... */}

                {voiceErrorMessage && (
                  <div className="mt-4 text-red-600 dark:text-red-400 text-sm bg-red-50 dark:bg-red-500/10 p-3 rounded-lg border border-red-200 dark:border-red-500/20">
                      {voiceErrorMessage}
                  </div>
                )}
                {!hasPermission && (
                    <div className="mt-4 text-yellow-700 dark:text-yellow-400 text-sm bg-yellow-50 dark:bg-yellow-500/10 p-3 rounded-lg border border-yellow-200 dark:border-yellow-500/20">
                        Please allow microphone access to use rehearsal mode.
                    </div>
                )}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Rehearsals;