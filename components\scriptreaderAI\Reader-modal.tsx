"use client"

import { useState, useEffect, useRef, use<PERSON><PERSON>back, useMemo } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { MessageSquare, FileText, Mic, Loader, Info, Book, X, MessageCircle, Video } from "lucide-react"
import { CompactThemeToggle } from "../ThemeToggle"
import ChatTab from "./ChatTab"
import { getFirestore, collection, query, where, getDocs, addDoc, serverTimestamp, orderBy, limit, doc, getDoc } from "firebase/firestore"
import { useGetNamespace } from "./useGetNamespace"
import { useSession } from "next-auth/react"
import { v4 as uuidv4 } from "uuid"
import useUpload, { StatusText } from "./useUpload"
import { useConversation } from "@elevenlabs/react"
import { db } from "components/firebase"
import SideBar from "./SideBar"
import Rehearsals from "./Rehearsals"
import FileDetails from "./FileDetails"
import ScriptTab from "./ScriptTab"
import ResponseTab from "./ResponseTab"
import { updateAgentVoice, getAgentConfiguration, extractAgentName, configureAgentClientTools, testElevenLabsConnection } from "./elevenlabs"
import { useAgentModality, type AgentModalityType } from "./useAgentModality"
import { AVAILABLE_VOICES } from "./voiceUtils"

interface ScriptFile {
  id: string
  name: string
  namespace: string
}

interface ChatMessage {
  id?: string
  tempId?: string
  role: "user" | "assistant"
  content: string
  timestamp: string
  audioUrl?: string
  fileDocumentId?: string
}

interface ReadermodalProps {
  isOpen: boolean
  onClose: () => void
  fileId?: string
}

function Readermodal({ isOpen, onClose, fileId }: ReadermodalProps) {
  const [activeTab, setActiveTab] = useState<string | null>(null)
  const [activeSection, setActiveSection] = useState<string>("rehearsing")
  const [scriptFiles, setScriptFiles] = useState<ScriptFile[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState<boolean>(false)
  const [uploadProgress, setUploadProgress] = useState<number | null>(null)
  const [uploadStatusText, setUploadStatusText] = useState<string>("Uploading script...")
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [chatId, setChatId] = useState<string | null>(null)
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [fileDocumentId, setFileDocumentId] = useState<string | null>(null)
  const [selectedFileNamespace, setSelectedFileNamespace] = useState<string | null>(null)
  const [fileName, setFileName] = useState<string | null>(null)
  const isMounted = useRef(true)
  const [hasPermission, setHasPermission] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [voiceErrorMessage, setVoiceErrorMessage] = useState("")
  const [apiConfigStatus, setApiConfigStatus] = useState<'unchecked' | 'valid' | 'invalid' | 'connecting'>('unchecked')
  const [detailedErrorInfo, setDetailedErrorInfo] = useState<string | null>(null)
  const { data: session, status: sessionStatus } = useSession()
  const userId = session?.user?.email || ""
  const { handleUpload, progress, status, error: uploadError } = useUpload()
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false)

  // Script-related state
  const [scriptContent, setScriptContent] = useState<string>("")
  const [isScriptLoading, setIsScriptLoading] = useState<boolean>(false)
  const [isScriptReady, setIsScriptReady] = useState<boolean>(false)
  const [isFormatting, setIsFormatting] = useState<boolean>(false)
  const [formattedMarkdown, setFormattedMarkdown] = useState<string>("")

  // Voice selection state
  const [selectedVoiceId, setSelectedVoiceId] = useState<string | null>(null)
  const [isUpdatingVoice, setIsUpdatingVoice] = useState(false)

  // Agent modality and configuration
  const { agentModality, setAgentModality, updateAgentPrompt } = useAgentModality()
  const [agentName, setAgentName] = useState<string | null>(null)
  const [agentConfigState, setAgentConfigState] = useState<'idle' | 'updating' | 'ready' | 'error'>('idle')
  const [agentConfigMessage, setAgentConfigMessage] = useState<string>('Select a voice and script to begin.')

  // Conversation logging
  const [conversationMessages, setConversationMessages] = useState<Array<{
    id: string
    type: "user" | "assistant"
    content: string
    timestamp: Date
  }>>([])
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null)
  const [sessionDuration, setSessionDuration] = useState(0)

  // Recording state
  const [isRecording, setIsRecording] = useState(false)
  const [recordingError, setRecordingError] = useState<string | null>(null)
  const [recordingMode, setRecordingMode] = useState<'audio' | 'video'>('audio')
  const [recordingDuration, setRecordingDuration] = useState(0)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const [currentStream, setCurrentStream] = useState<MediaStream | null>(null)
  const [hasCameraPermission, setHasCameraPermission] = useState(false)
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null)
  const [recordings, setRecordings] = useState<Array<{
    id: string
    filename: string
    url: string
    timestamp: Date
    rehearsalId: string
    type: 'audio' | 'video'
    duration?: number
    fileSize?: number
  }>>([])
  const [playingRecording, setPlayingRecording] = useState<string | null>(null)
  const [audioElements, setAudioElements] = useState<Record<string, HTMLAudioElement>>({})

  const clientTools = useMemo(() => {
    const tools: Record<string, (parameters: any) => any> = {}
    tools['switch_to_script_tab'] = (parameters: any) => {
      console.log("[CLIENT_TOOL] switch_to_script_tab called with:", parameters)
      const isReady = parameters?.ready === true || parameters?.user_ready === true
      if (isReady) {
        console.log("[CLIENT_TOOL] User confirmed ready")
        return {
          success: true,
          message: "User readiness confirmed. Please manually navigate to the Script tab."
        }
      } else {
        console.warn("[CLIENT_TOOL] 'ready' parameter not true:", parameters)
        return {
          success: false,
          message: "User readiness not confirmed."
        }
      }
    }
    return tools
  }, [])

  const conversation = useConversation({
    apiKey: process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************',
    agentId: process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq',
    maxReconnectAttempts: 3,
    reconnectInterval: 2000,
    clientTools,
    onConnect: () => {
      console.log("Connected to ElevenLabs API")
      setApiConfigStatus('valid')
      setDetailedErrorInfo(null)
      setSessionStartTime(new Date())
      setConversationMessages([])
      setVoiceErrorMessage("")
    },
    onDisconnect: (reason?: any) => {
      console.log("Disconnected from ElevenLabs:", reason)
      setIsListening(false)
      setSessionStartTime(null)
      setSessionDuration(0)
      setConversationMessages([])
      if (apiConfigStatus === 'valid' && reason !== 'user_initiated') {
        setVoiceErrorMessage("Connection lost. Click 'Start Rehearsing' to reconnect.")
        setApiConfigStatus('unchecked')
      } else {
        setVoiceErrorMessage("")
        setApiConfigStatus('unchecked')
      }
    },
    onError: (error: unknown) => {
      console.error("ElevenLabs API error:", error)
      let errorMessage = "Connection error occurred."
      if (error && typeof error === 'object' && 'type' in error && error.type === 'close') {
        const closeCode = (error as any).code
        const closeReason = (error as any).reason
        if (closeCode === 1006) errorMessage = "Connection lost abnormally."
        else if (closeCode === 1011) errorMessage = "Server error occurred."
        else if (closeCode === 4001) errorMessage = "Authentication failed."
        else if (closeCode === 4003) errorMessage = "Agent not found."
        else if (closeReason) errorMessage = `Connection closed: ${closeReason}`
        setDetailedErrorInfo(`WebSocket closed with code ${closeCode}: ${closeReason || 'No reason provided'}`)
      } else {
        errorMessage = error instanceof Error ? error.message : String(error)
        setDetailedErrorInfo(errorMessage)
      }
      setVoiceErrorMessage(errorMessage)
      setApiConfigStatus('invalid')
      setIsListening(false)
    },
    onMessage: (message) => {
      if (typeof message === "string") {
        console.log("[AUDIO] Voice input:", message)
        setConversationMessages(prev => [...prev, {
          id: uuidv4(),
          type: "user",
          content: message,
          timestamp: new Date()
        }])
      } else if (message && 'message' in message) {
        console.log("[AUDIO] AI response:", message.message)
        setConversationMessages(prev => [...prev, {
          id: uuidv4(),
          type: "assistant",
          content: message.message,
          timestamp: new Date()
        }])
      }
    },
    onToolCall: (toolCall: any) => {
      console.log("[TOOL_CALL] Tool called:", toolCall)
      if (toolCall?.name === 'switch_to_script_tab' || toolCall?.name === 'load_script_for_rehearsal') {
        const isReady = toolCall.parameters?.ready === true || toolCall.parameters?.user_ready === true
        if (isReady) {
          console.log("[TOOL_CALL] User confirmed ready")
          return {
            success: true,
            message: "User readiness confirmed. Please manually navigate to the Script tab."
          }
        } else {
          console.warn("[TOOL_CALL] 'ready' not true:", toolCall.parameters)
          return {
            success: false,
            message: "User readiness not confirmed."
          }
        }
      }
      console.warn("[TOOL_CALL] Unknown tool:", toolCall?.name)
      return {
        success: false,
        message: `Unknown tool: ${toolCall?.name || 'undefined'}`
      }
    },
    onAudioPlay: () => console.log("[AUDIO] Audio playback started"),
    onAudioStop: () => console.log("[AUDIO] Audio playback stopped")
  })

  const { status: voiceStatus, isSpeaking } = conversation

  // Configure agent in the background
  useEffect(() => {
    const configureAgentForRehearsal = async () => {
      if (isListening || !selectedVoiceId || !activeTab || !fileName || !agentName) {
        if (!selectedVoiceId) setAgentConfigMessage('Please select a character voice.')
        else if (!activeTab) setAgentConfigMessage('Please select a script.')
        else setAgentConfigState('idle')
        return
      }

      if (agentConfigState === 'ready') return

      console.log('[AGENT_CONFIG] Starting agent configuration...')
      setAgentConfigState('updating')
      setAgentConfigMessage(`Configuring ${agentName} for ${agentModality} mode...`)

      try {
        const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID
        const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY
        if (!agentId || !apiKey) throw new Error("Missing ElevenLabs configuration.")

        await updateAgentPrompt(agentId, apiKey, { scriptName: fileName, agentName })
        console.log('[AGENT_CONFIG] Agent configuration successful!')
        setAgentConfigState('ready')
        setAgentConfigMessage(`${agentName} is ready for rehearsal.`)
      } catch (error) {
        console.error('[AGENT_CONFIG] Failed to configure agent:', error)
        setAgentConfigState('error')
        setAgentConfigMessage('Error preparing agent.')
        setDetailedErrorInfo(error instanceof Error ? error.message : String(error))
      }
    }

    configureAgentForRehearsal()
  }, [agentModality, selectedVoiceId, activeTab, fileName, agentName, updateAgentPrompt, isListening, agentConfigState])

  // Ensure client tools are registered
  useEffect(() => {
    const ensureToolRegistration = async () => {
      if (!isOpen) return
      try {
        const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq'
        const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************'
        console.log('[TOOL_REGISTRATION] Ensuring tool registration...')
        await configureAgentClientTools(agentId, apiKey)
        console.log('[TOOL_REGISTRATION] Tool registration completed')
      } catch (error) {
        console.error('[TOOL_REGISTRATION] Failed to register tool:', error)
      }
    }
    const timeoutId = setTimeout(ensureToolRegistration, 1000)
    return () => clearTimeout(timeoutId)
  }, [isOpen])

  // Fetch agent name
  const fetchAgentName = useCallback(async () => {
    try {
      const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID
      const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY
      if (!agentId || !apiKey) return
      const agentConfig = await getAgentConfiguration(agentId, apiKey)
      const extractedName = extractAgentName(agentConfig)
      setAgentName(extractedName)
    } catch (error) {
      console.warn('[AGENT_CONFIG] Failed to fetch agent name:', error)
      setAgentName('CastMate Assistant')
    }
  }, [])

  useEffect(() => {
    fetchAgentName()
  }, [])

  useEffect(() => {
    if (selectedVoiceId) {
      console.log(`[AGENT_MODALITY] Voice changed to ${selectedVoiceId}, re-fetching agent name...`)
      fetchAgentName()
    }
  }, [selectedVoiceId, fetchAgentName])

  // Handle voice selection
  const handleVoiceSelect = async (voiceId: string) => {
    if (isUpdatingVoice || voiceId === selectedVoiceId) return

    console.log(`[VOICE_SELECT] Starting voice selection for: ${voiceId}`)
    setIsUpdatingVoice(true)
    setAgentConfigState('updating')
    setAgentConfigMessage('Updating character voice...')
    setSelectedVoiceId(voiceId)

    try {
      const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID
      const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY
      if (!agentId || !apiKey) throw new Error("Missing ElevenLabs configuration.")

      await updateAgentVoice(agentId, voiceId, apiKey)
      await fetchAgentName()
    } catch (error) {
      console.error('[VOICE_SELECT] Error updating agent voice:', error)
      setAgentConfigState('error')
      setAgentConfigMessage('Failed to update voice.')
      setVoiceErrorMessage(error instanceof Error ? error.message : "Voice update failed.")
    } finally {
      setIsUpdatingVoice(false)
    }
  }

  const handleSetAgentModality = (modality: AgentModalityType) => {
    if (modality !== agentModality) {
      setAgentConfigState('idle')
      setAgentModality(modality)
    }
  }

  const handleStartConversation = async () => {
    if (agentConfigState !== 'ready') {
      setVoiceErrorMessage("Agent is not ready. Please wait for configuration to complete.")
      return
    }
    if (!hasPermission) {
      setVoiceErrorMessage("Microphone access is required.")
      return
    }
    if (!session?.user?.email) {
      setVoiceErrorMessage("You must be signed in.")
      return
    }

    try {
      setApiConfigStatus('connecting')
      setIsListening(true)
      setVoiceErrorMessage("Connecting to rehearsal session...")
      await conversation.startSession()
      console.log("[CONVERSATION_START] Conversation started successfully.")
    } catch (error) {
      console.error("[CONVERSATION_START] Error starting conversation:", error)
      setVoiceErrorMessage("Failed to start conversation.")
      setDetailedErrorInfo(error instanceof Error ? error.message : String(error))
      setIsListening(false)
      setApiConfigStatus('invalid')
    }
  }

  const handleEndConversation = async () => {
    try {
      setIsListening(false)
      await conversation.endSession()
    } catch (error) {
      console.error("Error ending conversation:", error)
      setVoiceErrorMessage("Failed to end conversation")
      setDetailedErrorInfo(error instanceof Error ? error.message : String(error))
    }
  }

  const toggleMute = async () => {
    try {
      if (voiceStatus !== 'connected') {
        setVoiceErrorMessage("Start a conversation to control volume")
        return
      }
      const newVolume = isMuted ? 1 : 0
      if (conversation.setVolume) {
        conversation.setVolume({ volume: newVolume })
        setIsMuted(!isMuted)
      } else {
        setVoiceErrorMessage("Volume control not available")
      }
    } catch (error) {
      console.error("[AUDIO] Error changing volume:", error)
      setVoiceErrorMessage("Failed to change volume")
      setDetailedErrorInfo(error instanceof Error ? error.message : String(error))
    }
  }

  const handleSwitchToScriptTab = () => {
    console.log('[SCRIPT_NAV] Switching to Script tab')
    setActiveSection('script')
  }

  // Recording functions
  const startSelfTakeRecording = async () => {
    if (!userId) {
      setRecordingError('Please sign in to record')
      return
    }
    try {
      setRecordingError(null)
      setRecordingDuration(0)
      let stream: MediaStream
      let mimeType: string
      if (recordingMode === 'video') {
        stream = await navigator.mediaDevices.getUserMedia({
          video: { width: { ideal: 1280 }, height: { ideal: 720 }, frameRate: { ideal: 30 } },
          audio: true
        })
        if (MediaRecorder.isTypeSupported('video/webm;codecs=vp9,opus')) {
          mimeType = 'video/webm;codecs=vp9,opus'
        } else if (MediaRecorder.isTypeSupported('video/webm')) {
          mimeType = 'video/webm'
        } else if (MediaRecorder.isTypeSupported('video/mp4')) {
          mimeType = 'video/mp4'
        } else {
          throw new Error('No supported video format found')
        }
      } else {
        stream = await navigator.mediaDevices.getUserMedia({ audio: true })
        mimeType = 'audio/webm;codecs=opus'
      }
      setCurrentStream(stream)
      const recorder = new MediaRecorder(stream, { mimeType })
      mediaRecorderRef.current = recorder
      const chunks: Blob[] = []
      recorder.ondataavailable = (e) => chunks.push(e.data)
      recorder.onstop = () => {
        const blob = new Blob(chunks, { type: mimeType })
        uploadSelfTakeRecording(blob)
        stream.getTracks().forEach(track => track.stop())
        setCurrentStream(null)
      }
      recordingTimerRef.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1)
      }, 1000)
      recorder.start()
      setIsRecording(true)
      console.log(`[SelfTake] Started ${recordingMode} recording with format: ${mimeType}`)
    } catch (error) {
      setRecordingError(`Failed to start ${recordingMode} recording. Check permissions.`)
      console.error('Recording error:', error)
    }
  }

  const stopSelfTakeRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current)
        recordingTimerRef.current = null
      }
    }
  }

  const uploadSelfTakeRecording = async (blob: Blob) => {
    if (!userId) return
    try {
      const formData = new FormData()
      const rehearsalId = `rehearsal-${Date.now()}`
      const isVideo = recordingMode === 'video'
      const fileExtension = isVideo ? (blob.type.includes('webm') ? 'webm' : 'mp4') : 'mp3'
      const filename = `selftake-${rehearsalId}.${fileExtension}`
      const apiEndpoint = isVideo ? "/api/selfTakeVideo" : "/api/selfTakeAudio"
      const fieldName = isVideo ? "video" : "audio"
      formData.append(fieldName, blob, filename)
      formData.append("userId", userId)
      formData.append("rehearsalId", rehearsalId)
      formData.append("scriptName", fileName || "Unknown Script")
      formData.append("duration", recordingDuration.toString())
      const response = await fetch(apiEndpoint, { method: "POST", body: formData })
      if (response.ok) {
        const data = await response.json()
        const newRecording = {
          id: data.id,
          filename: data.filename,
          url: data.url,
          timestamp: new Date(data.timestamp),
          rehearsalId,
          type: recordingMode,
          duration: data.duration || recordingDuration,
          fileSize: data.fileSize
        }
        setRecordings(prev => [newRecording, ...prev])
        setRecordingError(null)
        console.log(`[SelfTake] ${recordingMode} recording uploaded:`, newRecording)
      } else {
        const errorData = await response.json()
        setRecordingError(errorData.error || `Failed to save ${recordingMode} recording`)
      }
    } catch (error) {
      console.error('Upload error:', error)
      setRecordingError(`Failed to upload ${recordingMode} recording`)
    }
  }

  const toggleRecordingPlayback = (recording: any) => {
    const audioKey = recording.id
    if (playingRecording === audioKey) {
      if (audioElements[audioKey]) {
        audioElements[audioKey].pause()
        audioElements[audioKey].currentTime = 0
      }
      setPlayingRecording(null)
    } else {
      Object.values(audioElements).forEach(audio => {
        audio.pause()
        audio.currentTime = 0
      })
      setPlayingRecording(null)
      if (!audioElements[audioKey]) {
        const audio = new Audio(recording.url)
        audio.onended = () => setPlayingRecording(null)
        audio.onerror = () => {
          setRecordingError('Failed to play recording')
          setPlayingRecording(null)
        }
        setAudioElements(prev => ({ ...prev, [audioKey]: audio }))
        audio.play().then(() => setPlayingRecording(audioKey))
      } else {
        audioElements[audioKey].currentTime = 0
        audioElements[audioKey].play().then(() => setPlayingRecording(audioKey))
      }
    }
  }

  const deleteRecording = async (recording: any) => {
    if (!userId) return
    const recordingType = recording.type || 'audio'
    if (!confirm(`Delete ${recordingType} recording from ${recording.timestamp.toLocaleString()}?`)) return
    try {
      const apiEndpoint = recordingType === 'video' ? "/api/selfTakeVideo" : "/api/selfTakeAudio"
      const response = await fetch(`${apiEndpoint}?id=${encodeURIComponent(recording.id)}&userId=${encodeURIComponent(userId)}`, {
        method: "DELETE"
      })
      if (response.ok) {
        setRecordings(prev => prev.filter(r => r.id !== recording.id))
        if (playingRecording === recording.id) {
          if (audioElements[recording.id]) audioElements[recording.id].pause()
          setPlayingRecording(null)
        }
        setAudioElements(prev => {
          const newElements = { ...prev }
          delete newElements[recording.id]
          return newElements
        })
        console.log(`[SelfTake] ${recordingType} recording deleted:`, recording.id)
      } else {
        const errorData = await response.json()
        setRecordingError(errorData.error || `Failed to delete ${recordingType} recording`)
      }
    } catch (error) {
      console.error('Delete error:', error)
      setRecordingError(`Failed to delete ${recordingType} recording`)
    }
  }

  const loadAllRecordings = async () => {
    if (!userId) return
    try {
      const audioResponse = await fetch(`/api/selfTakeAudio?userId=${encodeURIComponent(userId)}`)
      const audioData = audioResponse.ok ? await audioResponse.json() : { recordings: [] }
      const videoResponse = await fetch(`/api/selfTakeVideo?userId=${encodeURIComponent(userId)}`)
      const videoData = videoResponse.ok ? await videoResponse.json() : { recordings: [] }
      const allRecordings = [
        ...(audioData.recordings || []).map((r: any) => ({ ...r, type: 'audio' })),
        ...(videoData.recordings || []).map((r: any) => ({ ...r, type: 'video' }))
      ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      setRecordings(allRecordings)
      console.log('[SelfTake] Loaded recordings:', {
        audio: audioData.recordings?.length || 0,
        video: videoData.recordings?.length || 0,
        total: allRecordings.length
      })
    } catch (error) {
      console.error('Error loading recordings:', error)
      setRecordingError('Failed to load recordings')
    }
  }

  const requestCameraPermission = async () => {
    try {
      console.log("[VIDEO] Requesting camera and microphone permission...")
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { width: 1280, height: 720 },
        audio: true
      })
      setHasCameraPermission(true)
      setRecordingError(null)
      console.log("[VIDEO] Camera and microphone permission granted")
      stream.getTracks().forEach(track => track.stop())
    } catch (error) {
      setRecordingError("Camera access denied - please enable in browser settings")
      console.error("[VIDEO] Error accessing camera:", error)
    }
  }

  // Permissions and initialization
  useEffect(() => {
    const requestMicPermission = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
        setHasPermission(true)
        setVoiceErrorMessage("")
        stream.getTracks().forEach(track => track.stop())
      } catch (error) {
        setHasPermission(false)
        setVoiceErrorMessage("Microphone access denied.")
      }
    }
    if (isOpen && activeSection === "rehearsing") {
      requestMicPermission()
      if (recordingMode === 'video') requestCameraPermission()
      testElevenLabsConnection().catch(err => console.error("Initial connection test failed", err))
    }
  }, [isOpen, activeSection, recordingMode])

  // Cleanup
  useEffect(() => {
    return () => {
      isMounted.current = false
      if (isRecording) stopSelfTakeRecording()
      if (recordingTimerRef.current) clearInterval(recordingTimerRef.current)
      if (currentStream) currentStream.getTracks().forEach(track => track.stop())
    }
  }, [isOpen, isRecording, currentStream])

  // Track session duration
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null
    if (sessionStartTime && isListening) {
      interval = setInterval(() => {
        const duration = Math.floor((Date.now() - sessionStartTime.getTime()) / 1000)
        setSessionDuration(duration)
      }, 1000)
    }
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [sessionStartTime, isListening])

  const { namespace, fileName: namespaceFileName } = useGetNamespace(userId, activeTab || null)

  useEffect(() => {
    if (namespaceFileName) setFileName(namespaceFileName)
  }, [namespaceFileName])

  useEffect(() => {
    if (status === StatusText.ERROR && uploadError) {
      setError(`Upload error: ${uploadError}`)
      setIsUploading(false)
      setUploadProgress(null)
    } else if (status === StatusText.UPLOADING) {
      setIsUploading(true)
      setUploadProgress(progress || 0)
      setUploadStatusText("Uploading script...")
    } else if (status === StatusText.PROCESSING) {
      setIsUploading(true)
      setUploadProgress(progress || 0)
      setUploadStatusText("Processing script...")
    } else if (status === StatusText.COMPLETED) {
      setIsUploading(false)
      setUploadProgress(null)
      fetchScriptFiles()
    }
  }, [status, progress, uploadError])

  const fetchScriptFiles = async () => {
    if (sessionStatus === "loading") return
    if (!session?.user?.email) {
      setError("Please sign in to access your scripts.")
      setLoading(false)
      return
    }
    try {
      const filesRef = collection(db, `users/${session.user.email}/files`)
      const q = query(filesRef, where("category", "==", "SceneMate"), orderBy("name", "asc"))
      const querySnapshot = await getDocs(q)
      const files: ScriptFile[] = querySnapshot.docs.map(doc => ({
        id: doc.id,
        name: doc.data().name || "Untitled Script",
        namespace: doc.data().namespace || doc.id
      })).sort((a, b) => a.name.localeCompare(b.name))
      setScriptFiles(files)
      if (!activeTab && files.length > 0) setActiveTab(files[0].id)
      setLoading(false)
    } catch (err) {
      console.error("Error fetching script files:", err)
      setError(`Failed to load scripts: ${err instanceof Error ? err.message : "Unknown error"}`)
      setLoading(false)
    }
  }

  const fetchFileDocumentId = async (namespace: string) => {
    if (!userId) return
    try {
      const filesRef = collection(db, `users/${userId}/files`)
      const q = query(filesRef, where("namespace", "==", namespace), limit(1))
      const querySnapshot = await getDocs(q)
      if (!querySnapshot.empty) {
        const fileDoc = querySnapshot.docs[0]
        setFileDocumentId(fileDoc.id)
        const fileData = fileDoc.data()
        if (fileData.name) setFileName(fileData.name)
      }
    } catch (err) {
      console.error("Error fetching file document ID:", err)
    }
  }

  const fetchScriptContent = async () => {
    if (!activeTab || !userId) {
      setError("No script selected or user not authenticated")
      return
    }
    setIsScriptLoading(true)
    setError(null)
    try {
      const fileDocRef = doc(db, `users/${userId}/files/${activeTab}`)
      const fileDocSnap = await getDoc(fileDocRef)
      if (!fileDocSnap.exists()) {
        setError("Script file not found")
        setIsScriptLoading(false)
        return
      }
      const fileData = fileDocSnap.data()
      const fileNamespace = fileData.namespace || activeTab
      const chunksRef = collection(db, `users/${userId}/byteStoreCollection`)
      const q = query(chunksRef, where("metadata.doc_id", "==", fileNamespace))
      const querySnapshot = await getDocs(q)
      const chunks = querySnapshot.docs.map(d => d.data())
      if (chunks.length === 0) {
        setError("No content found for this script")
        setIsScriptLoading(false)
        return
      }
      chunks.sort((a, b) => (a.position || a.metadata?.page_number || 0) - (b.position || b.metadata?.page_number || 0))
      const contentField = "pageContent" in chunks[0] ? "pageContent" : "content"
      const content = chunks.map(chunk => chunk[contentField] || "").join("\n")
      setScriptContent(content)
      setIsScriptReady(true)
    } catch (err) {
      console.error("Error fetching script content:", err)
      setError("Failed to load script content")
      setIsScriptLoading(false)
    } finally {
      setIsScriptLoading(false)
    }
  }

  const createNewChat = async (fileNamespace?: string, fileDocId?: string) => {
    if (!userId) {
      setError("Please sign in to create a new chat.")
      return null
    }
    try {
      let firstMessageText = "New Rehearsal"
      const chatsRef = collection(db, `users/${userId}/chats`)
      let actualFileDocId: string
      let actualNamespace: string
      if (fileNamespace && fileDocId) {
        actualNamespace = fileNamespace
        actualFileDocId = fileDocId
      } else {
        const recentFile = await fetchMostRecentFile()
        if (recentFile) {
          actualNamespace = recentFile.namespace
          actualFileDocId = recentFile.id
          setFileName(recentFile.name)
        } else {
          setError("Please upload or select a file before creating a chat.")
          return null
        }
      }
      const chatData = {
        createdAt: serverTimestamp(),
        userId,
        firstMessage: firstMessageText,
        lastUpdated: serverTimestamp(),
        fileNamespace: actualNamespace,
        fileDocumentId: actualFileDocId
      }
      const docRef = await addDoc(chatsRef, chatData)
      const newChatId = docRef.id
      setChatId(newChatId)
      setChatMessages([])
      setSelectedFileNamespace(actualNamespace)
      setFileDocumentId(actualFileDocId)
      return newChatId
    } catch (err) {
      setError("Failed to create new chat: " + (err instanceof Error ? err.message : "Unknown error"))
      return null
    }
  }

  const fetchMostRecentFile = async (): Promise<{id: string, namespace: string, name: string} | null> => {
    if (!userId) return null
    try {
      const filesRef = collection(db, `users/${userId}/files`)
      const q = query(filesRef, orderBy("createdAt", "desc"), limit(1))
      const querySnapshot = await getDocs(q)
      if (!querySnapshot.empty) {
        const fileDoc = querySnapshot.docs[0]
        const fileData = fileDoc.data()
        return {
          id: fileDoc.id,
          namespace: fileData.namespace || fileDoc.id,
          name: fileData.name || "Untitled Document"
        }
      }
      return null
    } catch (err) {
      console.error("Error fetching most recent file:", err)
      return null
    }
  }

  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file || !userId) {
      setError("No file selected or user not authenticated.")
      return
    }
    try {
      const docId = uuidv4()
      await handleUpload(file, null, userId, docId)
      const newChatId = await createNewChat(docId, docId)
      if (newChatId) {
        setSelectedFileNamespace(docId)
        setFileDocumentId(docId)
        setFileName(file.name)
        setChatId(newChatId)
        const messagesRef = collection(db, `users/${userId}/chats/${newChatId}/messages`)
        const welcomeMessageData = {
          role: "assistant",
          text: "File processed successfully! How can I assist with your script?",
          createdAt: serverTimestamp(),
          fileDocumentId: docId
        }
        const welcomeDocRef = await addDoc(messagesRef, welcomeMessageData)
        const initialMessage: ChatMessage = {
          id: welcomeDocRef.id,
          role: "assistant",
          content: welcomeMessageData.text,
          timestamp: new Date().toISOString(),
          fileDocumentId: docId
        }
        setChatMessages([initialMessage])
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Upload failed")
    }
  }, [userId, handleUpload, createNewChat])

  const handleUploadClick = () => {
    if (fileInputRef.current) fileInputRef.current.click()
  }

  const handleScriptDeleted = useCallback(async () => {
    console.log("Script deleted, updating UI...")
    const deletedScriptId = activeTab
    setActiveTab(null)
    setFileName(null)
    setScriptContent("")
    setIsScriptLoading(false)
    setIsScriptReady(false)
    setIsFormatting(false)
    setFormattedMarkdown("")
    setError(null)
    try {
      const filesRef = collection(db, `users/${session?.user?.email}/files`)
      const q = query(filesRef, where("category", "==", "SceneMate"), orderBy("name", "asc"))
      const querySnapshot = await getDocs(q)
      const files: ScriptFile[] = querySnapshot.docs.map(doc => ({
        id: doc.id,
        name: doc.data().name || "Untitled Script",
        namespace: doc.data().namespace || doc.id
      })).sort((a, b) => a.name.localeCompare(b.name))
      setScriptFiles(files)
      setLoading(false)
    } catch (err) {
      console.error("Error refreshing scripts:", err)
      setError(`Failed to refresh scripts: ${err instanceof Error ? err.message : "Unknown error"}`)
      setLoading(false)
    }
  }, [activeTab, session?.user?.email])

  useEffect(() => {
    if (activeSection === "script" && activeTab) fetchScriptContent()
  }, [activeSection, activeTab])

  useEffect(() => {
    if (isScriptReady && scriptContent && !isFormatting) {
      const formatScriptContent = async () => {
        setIsFormatting(true)
        try {
          setFormattedMarkdown(scriptContent)
        } catch (err) {
          console.error("Error formatting script:", err)
          setFormattedMarkdown(scriptContent)
        } finally {
          setIsFormatting(false)
        }
      }
      formatScriptContent()
    }
  }, [isScriptReady, scriptContent, isFormatting])

  useEffect(() => {
    if (sessionStatus === "authenticated") fetchScriptFiles()
  }, [sessionStatus, userId])

  useEffect(() => {
    if (activeTab) {
      const selectedFile = scriptFiles.find(f => f.id === activeTab)
      setFileName(selectedFile ? selectedFile.name : null)
      setAgentConfigState('idle')
    } else {
      setFileName(null)
    }
  }, [activeTab, scriptFiles])

  useEffect(() => {
    if (fileId) setActiveTab(fileId)
  }, [fileId])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 dark:bg-black/80 backdrop-blur-sm p-2 sm:p-4 md:p-0">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="relative w-full max-w-full sm:max-w-3xl md:max-w-4xl lg:max-w-6xl h-[90vh] sm:h-[85vh] bg-white dark:bg-gray-900 rounded-2xl shadow-2xl overflow-hidden border border-gray-200 dark:border-gray-700"
      >
        <div className="absolute top-4 left-4 flex flex-col space-y-1">
          {sessionStatus === "loading" && (
            <div className="bg-yellow-500/20 text-yellow-300 text-xs px-2 py-1 rounded-md flex items-center">
              <Loader className="w-3 h-3 mr-1 animate-spin" />
              Loading session...
            </div>
          )}
          {sessionStatus === "unauthenticated" && (
            <div className="bg-red-500/20 text-red-300 text-xs px-2 py-1 rounded-md flex items-center">
              <Info className="w-3 h-3 mr-1" />
              Not authenticated
            </div>
          )}
          {apiConfigStatus === 'connecting' && (
            <div className="bg-yellow-500/20 text-yellow-300 text-xs px-2 py-1 rounded-md flex items-center">
              <Loader className="w-3 h-3 mr-1 animate-spin" />
              Connecting to voice API...
            </div>
          )}
          {apiConfigStatus === 'invalid' && (
            <div className="bg-red-500/20 text-red-300 text-xs px-2 py-1 rounded-md flex items-center">
              <Info className="w-3 h-3 mr-1" />
              Voice API issue
            </div>
          )}
        </div>

        <div className="flex flex-col md:flex-row h-full">
          <AnimatePresence>
            {(isMobileSidebarOpen || isOpen) && (
              <motion.div
                key="sidebar-motion"
                initial={{ x: -300, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: -300, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <SideBar
                  activeTab={activeTab}
                  setActiveTab={setActiveTab}
                  scriptFiles={scriptFiles}
                  loading={loading}
                  error={error}
                  setError={setError}
                  isUploading={isUploading}
                  uploadProgress={uploadProgress}
                  uploadStatusText={uploadStatusText}
                  handleUploadClick={handleUploadClick}
                  handleFileUpload={handleFileUpload}
                  fileInputRef={fileInputRef}
                  sessionStatus={sessionStatus}
                  session={session}
                  onClose={onClose}
                  isMobileSidebarOpen={isMobileSidebarOpen}
                  setIsMobileSidebarOpen={setIsMobileSidebarOpen}
                />
              </motion.div>
            )}
          </AnimatePresence>

          <div className="flex-1 overflow-hidden">
            <div className="h-full flex flex-col">
              {isRecording && (
                <div className="bg-red-600/90 backdrop-blur-sm px-4 py-2 flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-300 rounded-full animate-pulse"></div>
                      <span className="text-white font-medium text-sm">Recording Self Tape</span>
                    </div>
                    <span className="text-red-100 text-xs">
                      Recording will continue while you navigate between tabs
                    </span>
                  </div>
                  <button
                    onClick={stopSelfTakeRecording}
                    className="bg-white/20 hover:bg-white/30 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                  >
                    Stop Recording
                  </button>
                </div>
              )}

              <div className="border-b border-gray-200 dark:border-gray-700 px-4 sm:px-6 py-3 sm:py-4 bg-gray-50 dark:bg-gray-800/50">
                <div className="flex flex-wrap gap-4 sm:space-x-6 pt-3 justify-between items-center">
                  <div className="flex flex-wrap gap-4 sm:space-x-6">
                    {[
                      { name: "rehearsing", label: "Connecting", icon: <Mic className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
                      { name: "chat", label: "Tutor", icon: <MessageSquare className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
                      { name: "script", label: "Script", icon: <Book className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
                      {
                        name: "response",
                        label: "Self Tape",
                        icon: <MessageCircle className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />,
                        hasRecordingIndicator: isRecording && activeSection !== "response"
                      },
                      { name: "details", label: "Details", icon: <FileText className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
                    ].map(section => (
                      <button
                        key={section.name}
                        onClick={() => setActiveSection(section.name)}
                        className={`relative text-xs sm:text-sm font-medium px-3 py-2 rounded-md transition-all duration-200 flex items-center border-b-2 ${
                          activeSection === section.name
                            ? "border-blue-500 dark:border-blue-400 text-blue-600 dark:text-blue-400 bg-blue-50/50 dark:bg-blue-500/10"
                            : "border-transparent text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-300 hover:border-blue-300 dark:hover:border-blue-500 hover:bg-gray-50 dark:hover:bg-white/5"
                        } ${
                          ['rehearsing', 'chat', 'response', 'details'].includes(section.name) ? 'courier-font' : ''
                        }`}
                      >
                        {section.icon}
                        {section.label}
                        {section.hasRecordingIndicator && (
                          <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                        )}
                      </button>
                    ))}
                  </div>
                  <div className="flex items-center space-x-2">
                    <CompactThemeToggle />
                    <button
                      onClick={onClose}
                      className="hidden md:flex p-1.5 rounded-full bg-gray-100 dark:bg-gray-600/80 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-600 dark:text-white transition-all duration-300 shadow-sm items-center justify-center border border-gray-200 dark:border-transparent"
                      aria-label="Close"
                      type="button"
                      title="Close Script Reader"
                    >
                      <X className="w-3.5 h-3.5" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="flex-1 overflow-y-auto p-4 sm:p-6 scrollbar-thin scrollbar-track-gray-100 dark:scrollbar-track-gray-800 scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={`${activeTab}-${activeSection}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2 }}
                    className="space-y-6 h-full"
                  >
                    {activeSection === "rehearsing" && (
                      <Rehearsals
                        agentConfigState={agentConfigState}
                        agentConfigMessage={agentConfigMessage}
                        apiConfigStatus={apiConfigStatus}
                        detailedErrorInfo={detailedErrorInfo}
                        isListening={isListening}
                        voiceStatus={voiceStatus}
                        isMuted={isMuted}
                        isSpeaking={isSpeaking}
                        hasPermission={hasPermission}
                        voiceErrorMessage={voiceErrorMessage}
                        toggleMute={toggleMute}
                        handleEndConversation={handleEndConversation}
                        handleStartConversation={handleStartConversation}
                        setVoiceErrorMessage={setVoiceErrorMessage}
                        selectedScriptName={fileName || undefined}
                        selectedVoiceId={selectedVoiceId}
                        onVoiceSelect={handleVoiceSelect}
                        isUpdatingVoice={isUpdatingVoice}
                        agentModality={agentModality}
                        onAgentModalityChange={handleSetAgentModality}
                        onSwitchToScriptTab={handleSwitchToScriptTab}
                        conversationMessages={conversationMessages}
                      />
                    )}
                    {activeSection === "chat" && <ChatTab chatId={activeTab ?? ''} namespace={namespace} />}
                    {activeSection === "script" && (
                      <ScriptTab
                        scriptContent={scriptContent}
                        isScriptLoading={isScriptLoading || isFormatting}
                        isScriptReady={isScriptReady && !isFormatting}
                        scriptName={fileName}
                        scriptId={activeTab}
                        isListening={isListening}
                        isMuted={isMuted}
                        toggleMute={toggleMute}
                        handleEndConversation={handleEndConversation}
                        handleStartConversation={handleStartConversation}
                        onScriptDeleted={handleScriptDeleted}
                        apiConfigStatus={apiConfigStatus}
                        hasPermission={hasPermission}
                        voiceStatus={voiceStatus}
                        selectedVoiceId={selectedVoiceId}
                      />
                    )}
                    {activeSection === "response" && (
                      <ResponseTab
                        isConnected={voiceStatus === 'connected'}
                        conversationMessages={conversationMessages}
                        onClearMessages={() => setConversationMessages([])}
                        scriptName={fileName}
                        voiceId={selectedVoiceId}
                        sessionDuration={sessionDuration}
                        voiceStatus={voiceStatus}
                        isRecording={isRecording}
                        recordingError={recordingError}
                        recordings={recordings}
                        playingRecording={playingRecording}
                        recordingMode={recordingMode}
                        recordingDuration={recordingDuration}
                        currentStream={currentStream}
                        hasCameraPermission={hasCameraPermission}
                        onStartRecording={startSelfTakeRecording}
                        onStopRecording={stopSelfTakeRecording}
                        onTogglePlayback={toggleRecordingPlayback}
                        onDeleteRecording={deleteRecording}
                        onRecordingModeChange={setRecordingMode}
                        onLoadRecordings={loadAllRecordings}
                        onRequestCameraPermission={requestCameraPermission}
                      />
                    )}
                    {activeSection === "details" && (
                      <FileDetails
                        activeTab={activeTab}
                        fileName={fileName}
                        namespace={namespace}
                        apiConfigStatus={apiConfigStatus}
                        sessionStatus={sessionStatus}
                        detailedErrorInfo={detailedErrorInfo}
                      />
                    )}
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export { Readermodal }
