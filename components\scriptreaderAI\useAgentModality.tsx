"use client"

import { useState, useCallback } from 'react'
import { getAgentConfiguration, createElevenLabsClient, configureAgentClientTools } from "./elevenlabs"

export type AgentModalityType = 'conversational' | 'direct' | 'memorization'

interface ScriptContext {
  scriptName?: string | null
  // NOTE: scriptContent and characterInfo are intentionally removed.
  // The agent will get this information from its RAG knowledge base.
  agentName?: string | null
}

interface UseAgentModalityReturn {
  agentModality: AgentModalityType
  setAgentModality: (modality: AgentModalityType) => void
  updateAgentPrompt: (agentId: string, apiKey: string, scriptContext?: ScriptContext) => Promise<any>
  // NEW: Fast voice update function for optimized flow
  updateAgentVoiceOnly: (agentId: string, voiceId: string, apiKey: string) => Promise<any>
}

/**
 * Hook for managing agent modality state and configuration
 */
export function useAgentModality(): UseAgentModalityReturn {
  const [agentModality, setAgentModality] = useState<AgentModalityType>('conversational')

  const setAgentModalityWithLogging = useCallback((modality: AgentModalityType) => {
    console.log(`[AGENT_MODALITY] 🔄 Changing modality from "${agentModality}" to "${modality}"`)
    setAgentModality(modality)
  }, [agentModality])

  /**
   * Generates the appropriate system prompt based on the current modality and script context.
   */
  const generatePrompt = useCallback((modality: AgentModalityType, scriptContext?: ScriptContext): string => {
    const agentName = scriptContext?.agentName || 'CastMate Assistant'
    const scriptName = scriptContext?.scriptName || 'the script'

    const basePrompt = `You are ${agentName}, an AI acting coach and scene partner designed to help actors rehearse their lines and improve their performances.`

    const contextPrompt = `
SCRIPT CONTEXT:
- You are helping the user rehearse "${scriptName}"
- You have access to the full script content through your knowledge base
- Use your RAG knowledge to reference specific scenes, characters, and dialogue when helpful

CORE INSTRUCTIONS:
- Always stay in character as ${agentName}
- Be supportive, encouraging, and professional
- Provide constructive feedback on delivery, timing, and emotional expression
- Help the user practice difficult scenes or lines
- Offer suggestions for character interpretation and motivation`

    let modalitySpecificPrompt = ''

    switch (modality) {
      case 'conversational':
        modalitySpecificPrompt = `
CONVERSATIONAL MODE - Interactive Coaching:
- Engage in natural, flowing conversation about the script and performance
- Ask questions about character motivation and scene interpretation
- Provide detailed feedback and suggestions
- Encourage experimentation with different approaches
- Be warm, supportive, and collaborative in your coaching style
- Help the user explore the emotional depth of their character
- Offer multiple perspectives on how to approach challenging scenes`
        break

      case 'direct':
        modalitySpecificPrompt = `
DIRECT MODE - Focused Precision:
- Provide clear, concise, and specific feedback
- Focus on technical aspects: timing, diction, projection, and accuracy
- Give direct instructions without extensive explanation
- Be professional and efficient in your responses
- Prioritize precision and technical correctness
- Keep interactions brief and goal-oriented
- Focus on measurable improvements in performance`
        break

      case 'memorization':
        modalitySpecificPrompt = `
MEMORIZATION MODE - Line Learning Support:
- Help the user memorize their lines through repetition and practice
- Provide cues and prompts when they forget lines
- Break down long speeches into manageable chunks
- Use memory techniques like association and rhythm
- Be patient and encouraging during the memorization process
- Offer tips for retaining lines and building muscle memory
- Focus on accuracy and fluency of line delivery`
        break

      default:
        modalitySpecificPrompt = contextPrompt
    }

    const toolInstructions = `
AVAILABLE TOOLS:
- Use the switch_to_script_tab tool when the user confirms they are ready to begin rehearsal
- This tool helps coordinate the rehearsal session and user interface

RESPONSE STYLE:
- Keep responses conversational and natural
- Avoid overly formal or robotic language
- Show enthusiasm for the craft of acting
- Adapt your energy to match the user's needs and the scene requirements`

    return `${basePrompt}

${contextPrompt}

${modalitySpecificPrompt}

${toolInstructions}

Remember: You are ${agentName}, and you're here to help make this rehearsal session productive and enjoyable!`
  }, [])

  /**
   * Fast voice-only update function for optimized flow.
   * Only updates the agent's voice and name without changing the prompt.
   */
  const updateAgentVoiceOnly = useCallback(async (
    agentId: string,
    voiceId: string,
    apiKey: string
  ): Promise<any> => {
    console.log(`[AGENT_MODALITY] 🚀 Starting fast voice-only update for voice: "${voiceId}"`)

    try {
      // Get current agent configuration
      console.log('[AGENT_MODALITY] Retrieving current agent configuration...')
      const client = createElevenLabsClient(apiKey)
      const currentConfig = await getAgentConfiguration(agentId, apiKey)
      const conversationConfig = currentConfig.conversation_config || {}

      // Prepare the update payload with only voice changes
      const patchBody = {
        conversation_config: {
          ...conversationConfig,
          agent: {
            ...conversationConfig.agent,
            voice_id: voiceId
          },
          tts: {
            ...conversationConfig.tts,
            voice_id: voiceId
          }
        }
      }

      // Execute the fast voice update
      console.log(`[AGENT_MODALITY] Executing fast voice update to "${voiceId}"`)
      const updateResult = await client.conversationalAi.updateAgent(agentId, patchBody)

      console.log(`[AGENT_MODALITY] ✅ Fast voice update completed successfully!`)
      return updateResult

    } catch (error) {
      console.error(`[AGENT_MODALITY] ❌ Failed to update agent voice:`, error)
      throw error // Re-throw to be caught by the calling function
    }
  }, [])

  /**
   * Updates the ElevenLabs agent's system prompt and ensures tools are configured.
   */
  const updateAgentPrompt = useCallback(async (
    agentId: string,
    apiKey: string,
    scriptContext?: ScriptContext
  ): Promise<any> => {
    console.log(`[AGENT_MODALITY] 🚀 Starting agent prompt update process for modality: "${agentModality}"`)

    try {
      // 1. Generate the lean, instruction-based prompt
      const newPrompt = generatePrompt(agentModality, scriptContext)

      // 2. Ensure client tools are configured correctly (this is idempotent and safe to call)
      console.log('[AGENT_MODALITY] Ensuring client tools are configured...')
      await configureAgentClientTools(agentId, apiKey)

      // 3. Get current agent configuration to create the patch body
      console.log('[AGENT_MODALITY] Retrieving current agent configuration...')
      const client = createElevenLabsClient(apiKey)
      const currentConfig = await getAgentConfiguration(agentId, apiKey)
      const conversationConfig = currentConfig.conversation_config || {}

      // 4. Prepare the update payload with the new prompt
      const patchBody = {
        conversation_config: {
          ...conversationConfig,
          agent: {
            ...conversationConfig.agent,
            prompt: {
              ...conversationConfig.agent?.prompt,
              prompt: newPrompt
            }
          }
        }
      }

      // 5. Execute the update
      console.log(`[AGENT_MODALITY] Executing agent update with new prompt for "${agentModality}" mode.`)
      const updateResult = await client.conversationalAi.updateAgent(agentId, patchBody)

      console.log(`[AGENT_MODALITY] ✅ Agent prompt update completed successfully!`)
      return updateResult

    } catch (error) {
      console.error(`[AGENT_MODALITY] ❌ Failed to update agent prompt:`, error)
      throw error // Re-throw to be caught by the calling function
    }
  }, [agentModality, generatePrompt])

  return {
    agentModality,
    setAgentModality: setAgentModalityWithLogging,
    updateAgentPrompt,
    updateAgentVoiceOnly
  }
}