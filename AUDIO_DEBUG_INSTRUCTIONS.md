# Audio Debugging Instructions

## Issue: Cannot hear AI voice responses

The Reader-modal.tsx has been updated with enhanced audio debugging and management. Here's how to troubleshoot:

## Step 1: Check Browser Console

1. Open the Reader modal
2. Open browser Developer Tools (F12)
3. Go to Console tab
4. Run this command:
```javascript
debugAudioStatus()
```

This will show you detailed audio status information.

## Step 2: Check Audio Logs

Look for these log messages in the console:
- `[AUDIO] Audio context initialized`
- `[AUDIO] Audio playback started`
- `[AUDIO] Audio playback stopped`
- `[AUDIO] Toggling mute`

## Step 3: Test Audio Initialization

1. Click anywhere in the modal to trigger audio context initialization
2. Check console for: `[AUDIO] Audio context initialized`
3. Verify audio context state is "running"

## Step 4: Check ElevenLabs Connection

1. Select a voice (e.g., Mia)
2. Select a script
3. Click "Start Rehearsing"
4. Look for: `Connected to ElevenLabs API`
5. Speak into microphone
6. Check for: `[AUDIO] Audio playback started`

## Step 5: Browser Audio Settings

### Chrome/Edge:
1. Click the lock icon in address bar
2. Ensure "Sound" is set to "Allow"
3. Check if site is muted in browser tab

### System Audio:
1. Check Windows volume mixer
2. Ensure browser is not muted
3. Test with other audio (YouTube, etc.)

## Step 6: Manual Audio Test

Run this in console to test basic audio:
```javascript
// Test basic audio playback
const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT')
audio.play().then(() => console.log('Audio test successful')).catch(err => console.error('Audio test failed:', err))
```

## Common Issues & Solutions

### Issue 1: Audio Context Suspended
**Solution**: Click anywhere in the modal to resume audio context

### Issue 2: Browser Autoplay Policy
**Solution**: User interaction is required before audio can play

### Issue 3: No Audio Elements Found
**Solution**: ElevenLabs may not be creating audio elements properly

### Issue 4: Volume Set to 0
**Solution**: Check mute status and volume settings

## Advanced Debugging

### Check ElevenLabs WebSocket
1. Go to Network tab in DevTools
2. Filter by "WS" (WebSocket)
3. Look for ElevenLabs connections
4. Check for audio data streams

### Monitor Audio Elements
```javascript
// Monitor when audio elements are created
const observer = new MutationObserver((mutations) => {
  mutations.forEach((mutation) => {
    mutation.addedNodes.forEach((node) => {
      if (node.tagName === 'AUDIO') {
        console.log('[AUDIO_DEBUG] New audio element created:', node)
        node.addEventListener('play', () => console.log('[AUDIO_DEBUG] Audio started playing'))
        node.addEventListener('pause', () => console.log('[AUDIO_DEBUG] Audio paused'))
        node.addEventListener('error', (e) => console.log('[AUDIO_DEBUG] Audio error:', e))
      }
    })
  })
})
observer.observe(document.body, { childList: true, subtree: true })
```

## Expected Behavior

When working correctly, you should see:
1. Audio context initializes on first user interaction
2. ElevenLabs connects successfully
3. Audio elements are created for AI responses
4. Audio plays automatically when AI responds
5. Mute/unmute controls work properly

## Next Steps

If audio still doesn't work after these steps:
1. Try a different browser
2. Check ElevenLabs API status
3. Verify API key and agent configuration
4. Test with different voices
5. Check system audio drivers
